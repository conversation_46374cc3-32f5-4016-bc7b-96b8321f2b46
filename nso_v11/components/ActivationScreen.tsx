import { LinearGradient } from 'expo-linear-gradient';
import React, { useRef, useState } from 'react';
import {
    Alert,
    Animated,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { Colors, Spacing, Typography } from '../constants/theme';
import Button from './ui/Button';
import { StatusCard } from './ui/Card';
import Input from './ui/Input';

interface ActivationScreenProps {
  onActivationComplete: (activationKey: string) => void;
}

export default function ActivationScreen({ onActivationComplete }: ActivationScreenProps) {
  const [activationKey, setActivationKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleActivation = async () => {
    if (!activationKey.trim()) {
      Alert.alert('Error', 'Please enter an activation key');
      return;
    }

    if (activationKey.length < 8) {
      Alert.alert('Error', 'Activation key must be at least 8 characters long');
      return;
    }

    setIsLoading(true);

    // Simulate activation process
    setTimeout(() => {
      setIsLoading(false);
      // For demo purposes, accept any key with length >= 8
      onActivationComplete(activationKey);
    }, 2000);
  };

  const formatActivationKey = (text: string) => {
    // Remove any non-alphanumeric characters and convert to uppercase
    const cleaned = text.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    
    // Add dashes every 4 characters for better readability
    const formatted = cleaned.replace(/(.{4})/g, '$1-').replace(/-$/, '');
    
    return formatted;
  };

  const handleKeyChange = (text: string) => {
    const formatted = formatActivationKey(text);
    if (formatted.length <= 19) { // Max length: XXXX-XXXX-XXXX-XXXX
      setActivationKey(formatted);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.dark} />
      <LinearGradient
        colors={Colors.background.gradient}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.iconContainer}>
                <View style={styles.logoWrapper}>
                  <Image
                    source={require('../assets/images/logo1.jpeg')}
                    style={styles.logo1}
                    resizeMode="contain"
                  />
                  <Image
                    source={require('../assets/images/logo2.jpeg')}
                    style={styles.logo2}
                    resizeMode="contain"
                  />
                </View>
              </View>
              

            </View>

            {/* Activation Form */}
            <View style={styles.formContainer}>
              <Input
                label="Activation Key"
                value={activationKey}
                onChangeText={handleKeyChange}
                placeholder="XXXX-XXXX-XXXX-XXXX"
                autoCapitalize="characters"
                autoCorrect={false}
                maxLength={19}
                editable={!isLoading}
                required
                containerStyle={styles.inputContainer}
              />

              {/* Security Info */}
              <StatusCard status="info" style={styles.securityInfo}>
                <Text style={styles.securityTitle}>Security Features:</Text>
                <View style={styles.securityItem}>
                  <Text style={styles.securityBullet}>🔐</Text>
                  <Text style={styles.securityText}>One device per activation</Text>
                </View>
                <View style={styles.securityItem}>
                  <Text style={styles.securityBullet}>🔒</Text>
                  <Text style={styles.securityText}>Keys securely encrypted</Text>
                </View>
                <View style={styles.securityItem}>
                  <Text style={styles.securityBullet}>⏰</Text>
                  <Text style={styles.securityText}>Validity duration built-in</Text>
                </View>
              </StatusCard>

              {/* Activation Button */}
              <Button
                variant="primary"
                size="large"
                loading={isLoading}
                onPress={handleActivation}
                fullWidth
                style={styles.activateButton}
              >
                Activate
              </Button>

              {/* Help Text */}
              <Text style={styles.helpText}>
                Don't have an activation key? Contact your administrator or supervisor 
                for assistance.
              </Text>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Decorative Elements */}
        <View style={styles.decorativeElements}>
          <View style={[styles.circle, styles.circle1]} />
          <View style={[styles.circle, styles.circle2]} />
        </View>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  iconContainer: {
    marginBottom: Spacing.lg,
  },
  logoWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 15,
    padding: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  logo1: {
    width: 60,
    height: 60,
    marginRight: 8,
    borderRadius: 8,
  },
  logo2: {
    width: 60,
    height: 60,
    marginLeft: 8,
    borderRadius: 8,
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.light,
    textAlign: 'center',
    marginBottom: Spacing.base,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.muted,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.base,
  },
  formContainer: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: Spacing.lg,
  },
  securityInfo: {
    marginBottom: Spacing.lg,
  },
  securityTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.primary.main,
    marginBottom: Spacing.sm,
  },
  securityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  securityBullet: {
    fontSize: Typography.fontSize.base,
    marginRight: Spacing.sm,
  },
  securityText: {
    flex: 1,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
  },
  activateButton: {
    marginBottom: Spacing.lg,
  },
  helpText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.muted,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
  },
  decorativeElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  circle: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle1: {
    width: 150,
    height: 150,
    top: -75,
    right: -75,
  },
  circle2: {
    width: 100,
    height: 100,
    bottom: -50,
    left: -50,
  },
});
