import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { LocationData, locationService } from './locationService';

// Mock NetInfo for web compatibility
const NetInfo = {
  fetch: () => Promise.resolve({ isConnected: true, type: 'wifi' }),
  addEventListener: (callback: any) => {
    // Mock network listener
    return () => {};
  }
};

// API Configuration
const API_CONFIG = {
  BASE_URL: __DEV__
    ? 'http://localhost:3000/api/v1'  // Development - local backend
    : 'https://your-production-api.com/api/v1', // Production
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Storage Keys
const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  DEVICE_ID: 'device_id',
  SESSION_ID: 'session_id',
  PENDING_SYNCS: 'pending_syncs',
  LAST_SYNC_TIME: 'last_sync_time',
};

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
  timestamp: string;
}

export interface SyncResponse {
  success: boolean;
  syncId: string;
  processedCount?: number;
  conflicts?: any[];
  message?: string;
  timestamp: string;
}

export interface UserProfile {
  userId: string;
  fullName: string;
  role: string;
  facility: string;
  state: string;
  deviceId: string;
  activatedAt?: string;
  lastLoginAt?: string;
}

export interface DeviceInfo {
  deviceId: string;
  platform: string;
  osVersion: string;
  appVersion: string;
  deviceModel: string;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

class ApiService {
  private baseURL: string;
  private authToken: string | null = null;
  private deviceId: string | null = null;
  private sessionId: string | null = null;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.initializeService();
  }

  /**
   * Initialize the API service
   */
  private async initializeService() {
    try {
      // Load stored auth data
      this.authToken = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      this.deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID);
      this.sessionId = await AsyncStorage.getItem(STORAGE_KEYS.SESSION_ID);

      // Generate device ID if not exists
      if (!this.deviceId) {
        this.deviceId = await this.generateDeviceId();
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, this.deviceId);
      }

      // Generate new session ID
      this.sessionId = this.generateSessionId();
      await AsyncStorage.setItem(STORAGE_KEYS.SESSION_ID, this.sessionId);

    } catch (error) {
      console.error('Failed to initialize API service:', error);
    }
  }

  /**
   * Generate unique device ID
   */
  private async generateDeviceId(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const platform = Platform.OS;
    return `${platform}_${timestamp}_${random}`;
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get device information
   */
  private async getDeviceInfo(): Promise<DeviceInfo> {
    const { version } = require('../package.json');
    
    return {
      deviceId: this.deviceId!,
      platform: Platform.OS,
      osVersion: Platform.Version.toString(),
      appVersion: version || '1.0.0',
      deviceModel: Platform.OS === 'ios' ? 'iPhone' : 'Android Device'
    };
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    try {
      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        throw new Error('No internet connection');
      }

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Device-ID': this.deviceId || '',
        'X-Session-ID': this.sessionId || '',
        'X-App-Version': '1.0.0',
        ...options.headers as Record<string, string>,
      };

      // Add auth token if available
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      // Make request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse response
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;

    } catch (error) {
      console.error(`API request failed (attempt ${retryCount + 1}):`, error);

      // Retry logic
      if (retryCount < API_CONFIG.RETRY_ATTEMPTS) {
        await new Promise(resolve => 
          setTimeout(resolve, API_CONFIG.RETRY_DELAY * (retryCount + 1))
        );
        return this.makeRequest<T>(endpoint, options, retryCount + 1);
      }

      // Return error response
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: 'REQUEST_FAILED',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Activate device and create user account
   */
  async activateDevice(
    activationKey: string,
    userInfo: {
      fullName: string;
      role: string;
      facility: string;
      state: string;
      contactInfo: string;
    }
  ): Promise<ApiResponse<{ user: UserProfile; token: string; sessionId: string }>> {
    const deviceInfo = await this.getDeviceInfo();

    const response = await this.makeRequest<{ user: UserProfile; token: string; sessionId: string }>(
      '/auth/activate',
      {
        method: 'POST',
        body: JSON.stringify({
          activationKey,
          deviceInfo,
          userInfo,
        }),
      }
    );

    if (response.success && response.data) {
      // Store auth data
      this.authToken = response.data.token;
      this.sessionId = response.data.sessionId;
      
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));
      await AsyncStorage.setItem(STORAGE_KEYS.SESSION_ID, response.data.sessionId);
    }

    return response;
  }

  /**
   * Login with device credentials
   */
  async login(activationKey: string): Promise<ApiResponse<{ user: UserProfile; token: string; sessionId: string }>> {
    const response = await this.makeRequest<{ user: UserProfile; token: string; sessionId: string }>(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify({
          deviceId: this.deviceId,
          activationKey,
        }),
      }
    );

    if (response.success && response.data) {
      // Store auth data
      this.authToken = response.data.token;
      this.sessionId = response.data.sessionId;
      
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(response.data.user));
      await AsyncStorage.setItem(STORAGE_KEYS.SESSION_ID, response.data.sessionId);
    }

    return response;
  }

  /**
   * Logout user
   */
  async logout(): Promise<ApiResponse> {
    const response = await this.makeRequest('/auth/logout', {
      method: 'POST',
    });

    // Clear stored auth data
    this.authToken = null;
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.AUTH_TOKEN,
      STORAGE_KEYS.USER_DATA,
      STORAGE_KEYS.SESSION_ID,
    ]);

    return response;
  }

  /**
   * Verify auth token
   */
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user?: UserProfile }>> {
    if (!this.authToken) {
      return {
        success: false,
        error: 'No auth token',
        code: 'NO_TOKEN',
        timestamp: new Date().toISOString(),
      };
    }

    return this.makeRequest<{ valid: boolean; user?: UserProfile }>('/auth/verify');
  }

  /**
   * Upload data to server
   */
  async uploadData(
    dataType: string,
    data: any,
    syncType: 'full' | 'incremental' | 'manual' | 'automatic' = 'manual'
  ): Promise<SyncResponse> {
    const deviceInfo = await this.getDeviceInfo();
    const netInfo = await NetInfo.fetch();

    const response = await this.makeRequest<SyncResponse>('/sync/upload', {
      method: 'POST',
      body: JSON.stringify({
        syncType,
        dataType,
        data,
        deviceInfo,
        networkInfo: {
          connectionType: netInfo.type,
          isConnected: netInfo.isConnected,
        },
      }),
    });

    return response as SyncResponse;
  }

  /**
   * Download data from server
   */
  async downloadData(
    dataType?: string,
    lastSyncTime?: string,
    limit = 100
  ): Promise<ApiResponse<{ data: any; recordCount: number; hasMore: boolean }>> {
    const params = new URLSearchParams();
    if (dataType) params.append('dataType', dataType);
    if (lastSyncTime) params.append('lastSyncTime', lastSyncTime);
    params.append('limit', limit.toString());

    return this.makeRequest<{ data: any; recordCount: number; hasMore: boolean }>(
      `/sync/download?${params.toString()}`
    );
  }

  /**
   * Get sync status
   */
  async getSyncStatus(syncId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/sync/status/${syncId}`);
  }

  /**
   * Track user activity
   */
  async trackActivity(activity: {
    activityType: string;
    screen?: { name: string; route?: string; category?: string };
    action?: { name: string; target?: string; value?: any };
    performance?: { loadTime?: number; responseTime?: number };
    error?: { code: string; message: string; severity?: string };
    duration?: number;
  }): Promise<ApiResponse> {
    const deviceInfo = await this.getDeviceInfo();

    return this.makeRequest('/activity/track', {
      method: 'POST',
      body: JSON.stringify({
        ...activity,
        deviceContext: deviceInfo,
        timestamp: new Date().toISOString(),
      }),
    });
  }

  /**
   * Track multiple activities in batch
   */
  async trackActivitiesBatch(activities: any[]): Promise<ApiResponse> {
    return this.makeRequest('/activity/batch', {
      method: 'POST',
      body: JSON.stringify({
        activities,
      }),
    });
  }

  /**
   * Get user profile
   */
  async getUserProfile(): Promise<ApiResponse<UserProfile>> {
    return this.makeRequest<UserProfile>('/users/profile');
  }

  /**
   * Update user profile
   */
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    return this.makeRequest<UserProfile>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.authToken;
  }

  /**
   * Get stored user data
   */
  async getStoredUserData(): Promise<UserProfile | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to get stored user data:', error);
      return null;
    }
  }

  /**
   * Request location permissions
   */
  async requestLocationPermission(): Promise<LocationPermissionStatus> {
    return await locationService.requestLocationPermission();
  }

  /**
   * Get current location
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    return await locationService.getCurrentLocation();
  }

  /**
   * Get cached location from storage
   */
  async getCachedLocation(): Promise<LocationData | null> {
    return await locationService.getCachedLocation();
  }

  /**
   * Cache location data
   */
  async cacheLocation(location: LocationData): Promise<void> {
    return await locationService.cacheLocation(location);
  }

  /**
   * Get location with fallback to cached
   */
  async getLocationWithFallback(): Promise<LocationData | null> {
    return await locationService.getLocationWithFallback();
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
