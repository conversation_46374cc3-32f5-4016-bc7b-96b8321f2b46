const logger = require('../utils/logger');
const ClinicalRecord = require('../models/ClinicalRecord');
const Diagnosis = require('../models/Diagnosis');
const UserActivity = require('../models/UserActivity');
const User = require('../models/User');

class SyncService {
  
  /**
   * Process uploaded data from mobile app
   */
  async processUpload(syncRecord, data) {
    try {
      logger.info(`Processing upload for sync ${syncRecord.syncId}, type: ${syncRecord.dataType}`);
      
      let result = { success: false, processedCount: 0, conflicts: [] };
      
      switch (syncRecord.dataType) {
        case 'clinical_records':
          result = await this.processClinicalRecords(syncRecord, data);
          break;
          
        case 'diagnoses':
          result = await this.processDiagnoses(syncRecord, data);
          break;
          
        case 'user_activity':
          result = await this.processUserActivity(syncRecord, data);
          break;
          
        case 'user_profile':
          result = await this.processUserProfile(syncRecord, data);
          break;
          
        case 'app_settings':
          result = await this.processAppSettings(syncRecord, data);
          break;
          
        case 'offline_data':
          result = await this.processOfflineData(syncRecord, data);
          break;
          
        default:
          throw new Error(`Unsupported data type: ${syncRecord.dataType}`);
      }
      
      logger.info(`Upload processing completed for sync ${syncRecord.syncId}: ${result.processedCount} records`);
      return result;
      
    } catch (error) {
      logger.error(`Upload processing failed for sync ${syncRecord.syncId}:`, error);
      return {
        success: false,
        error: {
          code: 'UPLOAD_PROCESSING_FAILED',
          message: error.message
        }
      };
    }
  }
  
  /**
   * Process download request from mobile app
   */
  async processDownload(userId, options) {
    try {
      logger.info(`Processing download for user ${userId}, type: ${options.dataType}`);
      
      let result = { success: false, data: {}, recordCount: 0, hasMore: false };
      
      switch (options.dataType) {
        case 'clinical_records':
          result = await this.downloadClinicalRecords(userId, options);
          break;
          
        case 'diagnoses':
          result = await this.downloadDiagnoses(userId, options);
          break;
          
        case 'user_profile':
          result = await this.downloadUserProfile(userId, options);
          break;
          
        case 'app_settings':
          result = await this.downloadAppSettings(userId, options);
          break;
          
        case 'all':
          result = await this.downloadAllData(userId, options);
          break;
          
        default:
          throw new Error(`Unsupported download type: ${options.dataType}`);
      }
      
      logger.info(`Download processing completed for user ${userId}: ${result.recordCount} records`);
      return result;
      
    } catch (error) {
      logger.error(`Download processing failed for user ${userId}:`, error);
      return {
        success: false,
        error: {
          code: 'DOWNLOAD_PROCESSING_FAILED',
          message: error.message
        }
      };
    }
  }
  
  /**
   * Process clinical records upload
   */
  async processClinicalRecords(syncRecord, data) {
    const records = Array.isArray(data) ? data : data.records || [];
    let processedCount = 0;
    const conflicts = [];
    
    for (const recordData of records) {
      try {
        // Check if record already exists
        const existingRecord = await ClinicalRecord.findOne({
          recordId: recordData.recordId,
          userId: syncRecord.userId
        });
        
        if (existingRecord) {
          // Handle conflict resolution
          const conflict = await this.resolveRecordConflict(existingRecord, recordData, syncRecord);
          if (conflict) conflicts.push(conflict);
        } else {
          // Create new record
          await ClinicalRecord.create({
            ...recordData,
            userId: syncRecord.userId,
            deviceId: syncRecord.deviceId,
            syncedAt: new Date()
          });
        }
        
        processedCount++;
        
        // Update progress
        await syncRecord.updateProgress(processedCount, records.length);
        
      } catch (error) {
        logger.error(`Failed to process clinical record ${recordData.recordId}:`, error);
        // Continue processing other records
      }
    }
    
    return {
      success: true,
      processedCount,
      conflicts
    };
  }
  
  /**
   * Process diagnoses upload
   */
  async processDiagnoses(syncRecord, data) {
    const diagnoses = Array.isArray(data) ? data : data.diagnoses || [];
    let processedCount = 0;
    const conflicts = [];
    
    for (const diagnosisData of diagnoses) {
      try {
        const existingDiagnosis = await Diagnosis.findOne({
          diagnosisId: diagnosisData.diagnosisId,
          userId: syncRecord.userId
        });
        
        if (existingDiagnosis) {
          const conflict = await this.resolveDiagnosisConflict(existingDiagnosis, diagnosisData, syncRecord);
          if (conflict) conflicts.push(conflict);
        } else {
          await Diagnosis.create({
            ...diagnosisData,
            userId: syncRecord.userId,
            deviceId: syncRecord.deviceId,
            syncedAt: new Date()
          });
        }
        
        processedCount++;
        await syncRecord.updateProgress(processedCount, diagnoses.length);
        
      } catch (error) {
        logger.error(`Failed to process diagnosis ${diagnosisData.diagnosisId}:`, error);
      }
    }
    
    return {
      success: true,
      processedCount,
      conflicts
    };
  }
  
  /**
   * Process user activity upload
   */
  async processUserActivity(syncRecord, data) {
    const activities = Array.isArray(data) ? data : data.activities || [];
    let processedCount = 0;
    
    // Batch insert activities for better performance
    const activityDocs = activities.map(activity => ({
      ...activity,
      userId: syncRecord.userId,
      deviceId: syncRecord.deviceId,
      syncedAt: new Date()
    }));
    
    try {
      const result = await UserActivity.insertMany(activityDocs, { ordered: false });
      processedCount = result.length;
    } catch (error) {
      // Handle partial success in batch insert
      if (error.writeErrors) {
        processedCount = activityDocs.length - error.writeErrors.length;
        logger.warn(`Partial success in activity batch insert: ${processedCount}/${activityDocs.length}`);
      } else {
        throw error;
      }
    }
    
    return {
      success: true,
      processedCount,
      conflicts: []
    };
  }
  
  /**
   * Process user profile upload
   */
  async processUserProfile(syncRecord, data) {
    try {
      await User.findOneAndUpdate(
        { userId: syncRecord.userId },
        {
          ...data,
          lastSyncAt: new Date()
        },
        { upsert: true, new: true }
      );
      
      return {
        success: true,
        processedCount: 1,
        conflicts: []
      };
    } catch (error) {
      throw new Error(`Failed to update user profile: ${error.message}`);
    }
  }
  
  /**
   * Download clinical records for user
   */
  async downloadClinicalRecords(userId, options) {
    // For now, return empty data since we don't have the model yet
    // This will be implemented when ClinicalRecord model is created
    return {
      success: true,
      data: { clinical_records: [] },
      recordCount: 0,
      hasMore: false
    };
  }

  /**
   * Download diagnoses for user
   */
  async downloadDiagnoses(userId, options) {
    // For now, return empty data since we don't have the model yet
    return {
      success: true,
      data: { diagnoses: [] },
      recordCount: 0,
      hasMore: false
    };
  }

  /**
   * Download user profile
   */
  async downloadUserProfile(userId, options) {
    const user = await User.findOne({ userId }).lean();

    return {
      success: true,
      data: { user_profile: user },
      recordCount: user ? 1 : 0,
      hasMore: false
    };
  }

  /**
   * Download app settings
   */
  async downloadAppSettings(userId, options) {
    const user = await User.findOne({ userId }).select('preferences').lean();

    return {
      success: true,
      data: { app_settings: user?.preferences || {} },
      recordCount: 1,
      hasMore: false
    };
  }

  /**
   * Process app settings upload
   */
  async processAppSettings(syncRecord, data) {
    try {
      await User.findOneAndUpdate(
        { userId: syncRecord.userId },
        {
          preferences: data,
          lastSyncAt: new Date()
        }
      );

      return {
        success: true,
        processedCount: 1,
        conflicts: []
      };
    } catch (error) {
      throw new Error(`Failed to update app settings: ${error.message}`);
    }
  }

  /**
   * Process offline data upload
   */
  async processOfflineData(syncRecord, data) {
    // Process different types of offline data
    let totalProcessed = 0;
    const allConflicts = [];

    if (data.clinical_records) {
      const result = await this.processClinicalRecords(syncRecord, data.clinical_records);
      totalProcessed += result.processedCount;
      allConflicts.push(...result.conflicts);
    }

    if (data.diagnoses) {
      const result = await this.processDiagnoses(syncRecord, data.diagnoses);
      totalProcessed += result.processedCount;
      allConflicts.push(...result.conflicts);
    }

    if (data.user_activity) {
      const result = await this.processUserActivity(syncRecord, data.user_activity);
      totalProcessed += result.processedCount;
    }

    return {
      success: true,
      processedCount: totalProcessed,
      conflicts: allConflicts
    };
  }
  
  /**
   * Download all data for user
   */
  async downloadAllData(userId, options) {
    const [clinicalRecords, diagnoses, userProfile] = await Promise.all([
      this.downloadClinicalRecords(userId, options),
      this.downloadDiagnoses(userId, options),
      this.downloadUserProfile(userId, options)
    ]);
    
    return {
      success: true,
      data: {
        ...clinicalRecords.data,
        ...diagnoses.data,
        ...userProfile.data
      },
      recordCount: clinicalRecords.recordCount + diagnoses.recordCount + userProfile.recordCount,
      hasMore: clinicalRecords.hasMore || diagnoses.hasMore
    };
  }
  
  /**
   * Resolve conflicts between local and server data
   */
  async resolveRecordConflict(existingRecord, newData, syncRecord) {
    // Simple last-write-wins strategy for now
    // In production, you might want more sophisticated conflict resolution
    
    if (new Date(newData.updatedAt) > existingRecord.updatedAt) {
      await ClinicalRecord.findByIdAndUpdate(existingRecord._id, {
        ...newData,
        syncedAt: new Date()
      });
      
      return {
        recordId: newData.recordId,
        resolution: 'local_wins',
        resolvedAt: new Date()
      };
    }
    
    return null; // No conflict or server wins
  }
}

module.exports = new SyncService();
