{"name": "nso-backend", "version": "1.0.0", "description": "NSO Mobile App Backend - Data Sync and User Activity Tracking", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "seed": "node scripts/seedData.js"}, "keywords": ["nso", "healthcare", "mobile-backend", "sync", "medical"], "author": "NSO Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "winston": "^3.11.0", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}