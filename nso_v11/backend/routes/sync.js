const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

const SyncData = require('../models/SyncData');
const UserActivity = require('../models/UserActivity');
const User = require('../models/User');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const logger = require('../utils/logger');
const syncService = require('../services/syncService');

// Validation schemas
const syncUploadSchema = {
  syncType: { type: 'string', enum: ['full', 'incremental', 'manual', 'automatic'] },
  dataType: { type: 'string', enum: ['clinical_records', 'diagnoses', 'user_activity', 'app_settings', 'offline_data', 'media_files', 'user_profile', 'clinical_decisions'] },
  data: { type: 'object', required: true },
  deviceInfo: { type: 'object' },
  networkInfo: { type: 'object' }
};

// POST /api/v1/sync/upload - Upload data to server
router.post('/upload', auth, async (req, res) => {
  try {
    const { syncType, dataType, data, deviceInfo, networkInfo } = req.body;
    const { userId, deviceId } = req.user;

    // Create sync record
    const syncRecord = new SyncData({
      syncId: uuidv4(),
      userId,
      deviceId,
      syncType,
      syncDirection: 'upload',
      dataType,
      data,
      recordCount: Array.isArray(data) ? data.length : (data.records ? data.records.length : 1),
      dataSize: JSON.stringify(data).length,
      networkInfo,
      appVersion: deviceInfo?.appVersion,
      osVersion: deviceInfo?.osVersion,
      status: 'processing'
    });

    await syncRecord.save();

    // Log activity
    await UserActivity.create({
      activityId: uuidv4(),
      userId,
      deviceId,
      sessionId: req.sessionId || uuidv4(),
      activityType: 'sync_initiated',
      action: {
        name: 'sync_upload',
        target: dataType,
        value: syncType
      },
      syncContext: {
        syncId: syncRecord.syncId,
        dataType,
        recordCount: syncRecord.recordCount,
        dataSize: syncRecord.dataSize
      },
      deviceContext: deviceInfo
    });

    // Process sync data
    const result = await syncService.processUpload(syncRecord, data);

    if (result.success) {
      await syncRecord.markCompleted();
      
      // Update user's last sync time
      await User.findOneAndUpdate(
        { userId },
        { lastSyncAt: new Date() }
      );

      // Log successful sync
      await UserActivity.create({
        activityId: uuidv4(),
        userId,
        deviceId,
        sessionId: req.sessionId || uuidv4(),
        activityType: 'sync_completed',
        action: {
          name: 'sync_upload_completed',
          target: dataType,
          value: result.processedCount
        },
        syncContext: {
          syncId: syncRecord.syncId,
          dataType,
          recordCount: result.processedCount,
          syncDuration: syncRecord.duration
        }
      });

      res.status(200).json({
        success: true,
        syncId: syncRecord.syncId,
        message: 'Data uploaded successfully',
        processedCount: result.processedCount,
        conflicts: result.conflicts || [],
        timestamp: new Date().toISOString()
      });
    } else {
      await syncRecord.markFailed(result.error);
      
      // Log failed sync
      await UserActivity.create({
        activityId: uuidv4(),
        userId,
        deviceId,
        sessionId: req.sessionId || uuidv4(),
        activityType: 'sync_failed',
        action: {
          name: 'sync_upload_failed',
          target: dataType,
          value: result.error?.message
        },
        error: {
          code: result.error?.code || 'SYNC_FAILED',
          message: result.error?.message || 'Sync upload failed',
          severity: 'medium'
        },
        syncContext: {
          syncId: syncRecord.syncId,
          dataType
        }
      });

      res.status(400).json({
        success: false,
        syncId: syncRecord.syncId,
        error: result.error?.message || 'Sync failed',
        code: result.error?.code || 'SYNC_FAILED',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Sync upload error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during sync',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/v1/sync/download - Download data from server
router.get('/download', auth, async (req, res) => {
  try {
    const { userId, deviceId } = req.user;
    const { dataType, lastSyncTime, limit = 100 } = req.query;

    // Create sync record
    const syncRecord = new SyncData({
      syncId: uuidv4(),
      userId,
      deviceId,
      syncType: 'incremental',
      syncDirection: 'download',
      dataType: dataType || 'all',
      data: {},
      status: 'processing'
    });

    await syncRecord.save();

    // Log activity
    await UserActivity.create({
      activityId: uuidv4(),
      userId,
      deviceId,
      sessionId: req.sessionId || uuidv4(),
      activityType: 'sync_initiated',
      action: {
        name: 'sync_download',
        target: dataType || 'all',
        value: 'incremental'
      },
      syncContext: {
        syncId: syncRecord.syncId,
        dataType: dataType || 'all'
      }
    });

    // Get data from server
    const result = await syncService.processDownload(userId, {
      dataType,
      lastSyncTime: lastSyncTime ? new Date(lastSyncTime) : null,
      limit: parseInt(limit)
    });

    if (result.success) {
      syncRecord.data = result.data;
      syncRecord.recordCount = result.recordCount;
      syncRecord.dataSize = JSON.stringify(result.data).length;
      await syncRecord.markCompleted();

      res.status(200).json({
        success: true,
        syncId: syncRecord.syncId,
        data: result.data,
        recordCount: result.recordCount,
        hasMore: result.hasMore,
        lastSyncTime: new Date().toISOString(),
        timestamp: new Date().toISOString()
      });
    } else {
      await syncRecord.markFailed(result.error);
      
      res.status(400).json({
        success: false,
        syncId: syncRecord.syncId,
        error: result.error?.message || 'Download failed',
        code: result.error?.code || 'DOWNLOAD_FAILED',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Sync download error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during download',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/v1/sync/status/:syncId - Get sync status
router.get('/status/:syncId', auth, async (req, res) => {
  try {
    const { syncId } = req.params;
    const { userId } = req.user;

    const syncRecord = await SyncData.findOne({ syncId, userId });
    
    if (!syncRecord) {
      return res.status(404).json({
        success: false,
        error: 'Sync record not found',
        code: 'SYNC_NOT_FOUND'
      });
    }

    res.status(200).json({
      success: true,
      sync: {
        syncId: syncRecord.syncId,
        status: syncRecord.status,
        progress: syncRecord.progress,
        dataType: syncRecord.dataType,
        recordCount: syncRecord.recordCount,
        errors: syncRecord.errors,
        conflicts: syncRecord.conflicts,
        startedAt: syncRecord.startedAt,
        completedAt: syncRecord.completedAt,
        duration: syncRecord.duration
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Sync status error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/sync/history - Get sync history
router.get('/history', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { limit = 20, offset = 0, status, dataType } = req.query;

    const query = { userId };
    if (status) query.status = status;
    if (dataType) query.dataType = dataType;

    const syncs = await SyncData.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .select('-data'); // Exclude large data field

    const total = await SyncData.countDocuments(query);

    res.status(200).json({
      success: true,
      syncs,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < total
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Sync history error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/v1/sync/retry/:syncId - Retry failed sync
router.post('/retry/:syncId', auth, async (req, res) => {
  try {
    const { syncId } = req.params;
    const { userId } = req.user;

    const syncRecord = await SyncData.findOne({ syncId, userId });
    
    if (!syncRecord) {
      return res.status(404).json({
        success: false,
        error: 'Sync record not found',
        code: 'SYNC_NOT_FOUND'
      });
    }

    if (!syncRecord.canRetry()) {
      return res.status(400).json({
        success: false,
        error: 'Sync cannot be retried',
        code: 'RETRY_NOT_ALLOWED'
      });
    }

    await syncRecord.scheduleRetry();

    res.status(200).json({
      success: true,
      message: 'Sync scheduled for retry',
      syncId: syncRecord.syncId,
      nextRetryAt: syncRecord.nextRetryAt,
      retryCount: syncRecord.retryCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Sync retry error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/sync/stats - Get sync statistics
router.get('/stats', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 7 } = req.query;

    const stats = await SyncData.getSyncStats(userId, parseInt(days));

    // Calculate additional metrics
    const totalSyncs = stats.reduce((sum, stat) => sum + stat.count, 0);
    const successRate = totalSyncs > 0 ?
      (stats.find(s => s._id === 'completed')?.count || 0) / totalSyncs * 100 : 0;

    res.status(200).json({
      success: true,
      stats: {
        totalSyncs,
        successRate: Math.round(successRate * 100) / 100,
        byStatus: stats,
        period: `${days} days`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Sync stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
