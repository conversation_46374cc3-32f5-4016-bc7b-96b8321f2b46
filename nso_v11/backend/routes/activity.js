const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

const UserActivity = require('../models/UserActivity');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const logger = require('../utils/logger');

// Validation schemas
const activitySchema = {
  activityType: { 
    type: 'string', 
    required: true,
    enum: [
      'app_launch', 'app_close', 'screen_view', 'button_click', 'form_submit',
      'search', 'diagnosis_create', 'diagnosis_view', 'diagnosis_edit',
      'clinical_record_view', 'clinical_record_create', 'sync_initiated',
      'sync_completed', 'sync_failed', 'settings_change', 'profile_update',
      'login', 'logout', 'error_occurred', 'feature_used', 'help_accessed',
      'export_data', 'import_data'
    ]
  },
  screen: { type: 'object' },
  action: { type: 'object' },
  clinicalContext: { type: 'object' },
  performance: { type: 'object' },
  deviceContext: { type: 'object' },
  location: { type: 'object' },
  error: { type: 'object' },
  interaction: { type: 'object' },
  featureUsage: { type: 'object' },
  duration: { type: 'number' }
};

const batchActivitySchema = {
  activities: { type: 'array', required: true, items: activitySchema }
};

// POST /api/v1/activity/track - Track single activity
router.post('/track', auth, async (req, res) => {
  try {
    const { userId, deviceId } = req.user;
    const activityData = req.body;

    const activity = new UserActivity({
      activityId: uuidv4(),
      userId,
      deviceId,
      sessionId: req.sessionId || req.headers['x-session-id'] || uuidv4(),
      ...activityData,
      timestamp: new Date()
    });

    await activity.save();

    res.status(201).json({
      success: true,
      activityId: activity.activityId,
      message: 'Activity tracked successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Activity tracking error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to track activity',
      code: 'TRACKING_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

// POST /api/v1/activity/batch - Track multiple activities
router.post('/batch', auth, validate(batchActivitySchema), async (req, res) => {
  try {
    const { userId, deviceId } = req.user;
    const { activities } = req.body;
    const sessionId = req.sessionId || req.headers['x-session-id'] || uuidv4();

    const activityDocs = activities.map(activityData => ({
      activityId: uuidv4(),
      userId,
      deviceId,
      sessionId,
      ...activityData,
      timestamp: activityData.timestamp ? new Date(activityData.timestamp) : new Date()
    }));

    const result = await UserActivity.insertMany(activityDocs, { ordered: false });

    res.status(201).json({
      success: true,
      trackedCount: result.length,
      message: 'Activities tracked successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Batch activity tracking error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to track activities',
      code: 'BATCH_TRACKING_ERROR',
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/v1/activity/user - Get user's activity history
router.get('/user', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { 
      limit = 100, 
      offset = 0, 
      activityType, 
      startDate, 
      endDate,
      screen 
    } = req.query;

    const query = { userId };
    
    if (activityType) query.activityType = activityType;
    if (screen) query['screen.name'] = screen;
    
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    const activities = await UserActivity.find(query)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .lean();

    const total = await UserActivity.countDocuments(query);

    res.status(200).json({
      success: true,
      activities,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < total
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('User activity fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user activities',
      code: 'FETCH_ERROR'
    });
  }
});

// GET /api/v1/activity/session/:sessionId - Get session activities
router.get('/session/:sessionId', auth, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { userId } = req.user;

    const activities = await UserActivity.findBySession(sessionId)
      .where('userId').equals(userId);

    if (activities.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Session not found',
        code: 'SESSION_NOT_FOUND'
      });
    }

    // Calculate session metrics
    const sessionStart = activities[0].timestamp;
    const sessionEnd = activities[activities.length - 1].timestamp;
    const sessionDuration = sessionEnd.getTime() - sessionStart.getTime();
    
    const screenViews = activities.filter(a => a.activityType === 'screen_view');
    const uniqueScreens = [...new Set(screenViews.map(a => a.screen?.name))];
    
    const errors = activities.filter(a => a.activityType === 'error_occurred');

    res.status(200).json({
      success: true,
      session: {
        sessionId,
        activities,
        metrics: {
          duration: sessionDuration,
          activityCount: activities.length,
          screenViews: screenViews.length,
          uniqueScreens: uniqueScreens.length,
          errorCount: errors.length,
          startTime: sessionStart,
          endTime: sessionEnd
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Session activity fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch session activities',
      code: 'SESSION_FETCH_ERROR'
    });
  }
});

// GET /api/v1/activity/stats - Get activity statistics
router.get('/stats', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 7 } = req.query;

    const stats = await UserActivity.getUserStats(userId, parseInt(days));
    
    // Get additional metrics
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    const totalActivities = await UserActivity.countDocuments({
      userId,
      timestamp: { $gte: startDate }
    });
    
    const errorCount = await UserActivity.countDocuments({
      userId,
      activityType: 'error_occurred',
      timestamp: { $gte: startDate }
    });
    
    const uniqueSessions = await UserActivity.distinct('sessionId', {
      userId,
      timestamp: { $gte: startDate }
    });

    res.status(200).json({
      success: true,
      stats: {
        totalActivities,
        errorCount,
        sessionCount: uniqueSessions.length,
        dailyBreakdown: stats,
        period: `${days} days`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Activity stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch activity statistics',
      code: 'STATS_ERROR'
    });
  }
});

// GET /api/v1/activity/performance - Get performance metrics
router.get('/performance', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 7 } = req.query;

    const performanceMetrics = await UserActivity.getPerformanceMetrics(userId, parseInt(days));

    res.status(200).json({
      success: true,
      performance: performanceMetrics,
      period: `${days} days`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Performance metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch performance metrics',
      code: 'PERFORMANCE_ERROR'
    });
  }
});

// GET /api/v1/activity/errors - Get error activities
router.get('/errors', auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { severity, limit = 50, offset = 0 } = req.query;

    const query = { 
      userId,
      activityType: 'error_occurred'
    };
    
    if (severity) query['error.severity'] = severity;

    const errors = await UserActivity.find(query)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .lean();

    const total = await UserActivity.countDocuments(query);

    res.status(200).json({
      success: true,
      errors,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < total
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error activities fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch error activities',
      code: 'ERROR_FETCH_ERROR'
    });
  }
});

module.exports = router;
