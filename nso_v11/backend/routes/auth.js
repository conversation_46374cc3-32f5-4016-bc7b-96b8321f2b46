const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

const User = require('../models/User');
const UserActivity = require('../models/UserActivity');
const { generateToken, verifyActivationKey } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const logger = require('../utils/logger');

// Validation schemas
const activationSchema = {
  activationKey: { type: 'string', required: true },
  deviceInfo: {
    type: 'object',
    required: true,
    properties: {
      deviceId: { type: 'string', required: true },
      platform: { type: 'string', enum: ['ios', 'android', 'web'] },
      osVersion: { type: 'string' },
      appVersion: { type: 'string' },
      deviceModel: { type: 'string' }
    }
  },
  userInfo: {
    type: 'object',
    required: true,
    properties: {
      fullName: { type: 'string', required: true },
      role: { type: 'string', required: true, enum: ['Doctor', 'Nurse', 'Medical Officer', 'Healthcare Worker', 'Administrator'] },
      facility: { type: 'string', required: true },
      state: { type: 'string', required: true },
      contactInfo: { type: 'string', required: true }
    }
  }
};

const loginSchema = {
  deviceId: { type: 'string', required: true },
  activationKey: { type: 'string', required: true }
};

// POST /api/v1/auth/activate - Activate device and create user
router.post('/activate', async (req, res) => {
  try {
    const { activationKey, deviceInfo, userInfo } = req.body;
    
    // Check if device is already registered
    const existingUser = await User.findOne({ 
      'deviceInfo.deviceId': deviceInfo.deviceId 
    });
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'Device already registered',
        code: 'DEVICE_ALREADY_REGISTERED'
      });
    }
    
    // In production, you would validate the activation key against a secure database
    // For now, we'll use a simple validation
    const isValidKey = activationKey.length >= 8; // Simple validation
    
    if (!isValidKey) {
      logger.logSecurity('INVALID_ACTIVATION_KEY', null, {
        deviceId: deviceInfo.deviceId,
        activationKey: activationKey.substring(0, 4) + '****'
      });
      
      return res.status(400).json({
        success: false,
        error: 'Invalid activation key',
        code: 'INVALID_ACTIVATION_KEY'
      });
    }
    
    // Generate unique user ID
    const userId = `NSO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create new user
    const user = new User({
      userId,
      ...userInfo,
      deviceInfo: {
        ...deviceInfo,
        activationKey,
        isActivated: true,
        activatedAt: new Date()
      },
      isActive: true
    });
    
    await user.save();
    
    // Generate JWT token
    const token = generateToken(user);
    
    // Log activation activity
    await UserActivity.create({
      activityId: uuidv4(),
      userId,
      deviceId: deviceInfo.deviceId,
      sessionId: uuidv4(),
      activityType: 'login',
      action: {
        name: 'device_activation',
        target: 'auth_system',
        value: 'success'
      },
      deviceContext: deviceInfo,
      timestamp: new Date()
    });
    
    logger.logSecurity('DEVICE_ACTIVATED', userId, {
      deviceId: deviceInfo.deviceId,
      facility: userInfo.facility,
      role: userInfo.role
    });
    
    res.status(201).json({
      success: true,
      message: 'Device activated successfully',
      user: {
        userId: user.userId,
        fullName: user.fullName,
        role: user.role,
        facility: user.facility,
        state: user.state,
        deviceId: user.deviceInfo.deviceId,
        activatedAt: user.deviceInfo.activatedAt
      },
      token,
      expiresIn: '7d',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Device activation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during activation',
      code: 'ACTIVATION_ERROR'
    });
  }
});

// POST /api/v1/auth/login - Login with device
router.post('/login', async (req, res) => {
  try {
    const { deviceId, activationKey } = req.body;
    
    // Find user by device ID
    const user = await User.findOne({ 
      'deviceInfo.deviceId': deviceId,
      isActive: true 
    });
    
    if (!user) {
      logger.logSecurity('LOGIN_ATTEMPT_UNKNOWN_DEVICE', null, { deviceId });
      
      return res.status(401).json({
        success: false,
        error: 'Device not registered',
        code: 'DEVICE_NOT_REGISTERED'
      });
    }
    
    // Verify activation key
    if (!verifyActivationKey(activationKey, user.deviceInfo.activationKey)) {
      logger.logSecurity('LOGIN_ATTEMPT_INVALID_KEY', user.userId, { deviceId });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid activation key',
        code: 'INVALID_ACTIVATION_KEY'
      });
    }
    
    // Check if device is activated
    if (!user.deviceInfo.isActivated) {
      return res.status(401).json({
        success: false,
        error: 'Device not activated',
        code: 'DEVICE_NOT_ACTIVATED'
      });
    }
    
    // Update last login time
    await user.updateLastLogin();
    
    // Generate new JWT token
    const token = generateToken(user);
    
    // Log login activity
    const sessionId = uuidv4();
    await UserActivity.create({
      activityId: uuidv4(),
      userId: user.userId,
      deviceId,
      sessionId,
      activityType: 'login',
      action: {
        name: 'user_login',
        target: 'auth_system',
        value: 'success'
      },
      timestamp: new Date()
    });
    
    logger.logSecurity('USER_LOGIN', user.userId, { deviceId });
    
    res.status(200).json({
      success: true,
      message: 'Login successful',
      user: {
        userId: user.userId,
        fullName: user.fullName,
        role: user.role,
        facility: user.facility,
        state: user.state,
        deviceId: user.deviceInfo.deviceId,
        lastLoginAt: user.lastLoginAt
      },
      token,
      sessionId,
      expiresIn: '7d',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during login',
      code: 'LOGIN_ERROR'
    });
  }
});

// POST /api/v1/auth/logout - Logout user
router.post('/logout', async (req, res) => {
  try {
    const deviceId = req.headers['x-device-id'];
    const sessionId = req.headers['x-session-id'];
    
    if (deviceId && sessionId) {
      // Log logout activity
      const user = await User.findOne({ 'deviceInfo.deviceId': deviceId });
      
      if (user) {
        await UserActivity.create({
          activityId: uuidv4(),
          userId: user.userId,
          deviceId,
          sessionId,
          activityType: 'logout',
          action: {
            name: 'user_logout',
            target: 'auth_system',
            value: 'success'
          },
          timestamp: new Date()
        });
        
        logger.logSecurity('USER_LOGOUT', user.userId, { deviceId, sessionId });
      }
    }
    
    res.status(200).json({
      success: true,
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during logout',
      code: 'LOGOUT_ERROR'
    });
  }
});

// GET /api/v1/auth/verify - Verify token
router.get('/verify', async (req, res) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'No token provided',
        code: 'NO_TOKEN'
      });
    }
    
    const token = authHeader.substring(7);
    const jwt = require('jsonwebtoken');
    const config = require('../config');
    
    try {
      const decoded = jwt.verify(token, config.JWT_SECRET);
      
      const user = await User.findOne({ 
        userId: decoded.userId,
        'deviceInfo.deviceId': decoded.deviceId,
        isActive: true 
      });
      
      if (!user || !user.deviceInfo.isActivated) {
        return res.status(401).json({
          success: false,
          error: 'Invalid token or inactive user',
          code: 'INVALID_TOKEN'
        });
      }
      
      res.status(200).json({
        success: true,
        valid: true,
        user: {
          userId: user.userId,
          fullName: user.fullName,
          role: user.role,
          facility: user.facility,
          deviceId: user.deviceInfo.deviceId
        },
        expiresAt: new Date(decoded.exp * 1000).toISOString(),
        timestamp: new Date().toISOString()
      });
      
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        valid: false,
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }
    
  } catch (error) {
    logger.error('Token verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during verification',
      code: 'VERIFICATION_ERROR'
    });
  }
});

module.exports = router;
