const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Basic middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'NSO Backend Server is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Basic API endpoints
app.get('/api/v1/status', (req, res) => {
  res.json({
    status: 'success',
    message: 'NSO API is operational',
    endpoints: [
      'GET /health',
      'GET /api/v1/status',
      'POST /api/v1/auth/login',
      'GET /api/v1/users'
    ]
  });
});

// Mock auth endpoint
app.post('/api/v1/auth/login', (req, res) => {
  const { activationKey } = req.body;
  
  if (activationKey === 'NSO-DEMO-2024') {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'demo-jwt-token-' + Date.now(),
      user: {
        id: 'user_001',
        name: 'Demo User',
        role: 'healthcare_worker',
        facility: 'Demo Clinic'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid activation key'
    });
  }
});

// Mock users endpoint
app.get('/api/v1/users', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'user_001',
        name: 'Dr. Sarah Johnson',
        role: 'doctor',
        facility: 'General Hospital Lagos',
        status: 'active'
      },
      {
        id: 'user_002',
        name: 'Nurse Mary Wilson',
        role: 'nurse',
        facility: 'Primary Health Center',
        status: 'active'
      }
    ]
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ NSO Test Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API status: http://localhost:${PORT}/api/v1/status`);
});

module.exports = app;
