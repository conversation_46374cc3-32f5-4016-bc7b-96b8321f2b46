#!/bin/bash

# NSO Backend Startup Script
echo "🚀 Starting NSO Backend Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before running the server."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Check if MongoDB URL is configured
if grep -q "<db_password>" .env; then
    echo "⚠️  Please configure your MongoDB URL in .env file"
    echo "   Replace <db_password> with your actual database password"
    exit 1
fi

# Start the server
echo "🌟 Starting NSO Backend Server..."
echo "📊 Environment: $(grep NODE_ENV .env | cut -d'=' -f2)"
echo "🔌 Port: $(grep PORT .env | cut -d'=' -f2)"
echo ""

# Check if we should run in development or production mode
if grep -q "NODE_ENV=development" .env; then
    echo "🔧 Running in development mode with nodemon..."
    npm run dev
else
    echo "🚀 Running in production mode..."
    npm start
fi
