const mongoose = require('mongoose');

const syncDataSchema = new mongoose.Schema({
  // Sync Identification
  syncId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // User Information
  userId: {
    type: String,
    required: true,
    index: true
  },
  deviceId: {
    type: String,
    required: true,
    index: true
  },
  
  // Sync Metadata
  syncType: {
    type: String,
    required: true,
    enum: ['full', 'incremental', 'manual', 'automatic', 'conflict_resolution']
  },
  syncDirection: {
    type: String,
    required: true,
    enum: ['upload', 'download', 'bidirectional']
  },
  
  // Data Content
  dataType: {
    type: String,
    required: true,
    enum: [
      'clinical_records',
      'diagnoses', 
      'user_activity',
      'app_settings',
      'offline_data',
      'media_files',
      'user_profile',
      'clinical_decisions'
    ]
  },
  
  // Sync Data
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  
  // Data Statistics
  recordCount: {
    type: Number,
    default: 0
  },
  dataSize: {
    type: Number, // in bytes
    default: 0
  },
  
  // Sync Status
  status: {
    type: String,
    required: true,
    enum: ['pending', 'processing', 'completed', 'failed', 'partial'],
    default: 'pending'
  },
  
  // Progress Tracking
  progress: {
    total: {
      type: Number,
      default: 0
    },
    completed: {
      type: Number,
      default: 0
    },
    failed: {
      type: Number,
      default: 0
    },
    percentage: {
      type: Number,
      default: 0
    }
  },
  
  // Error Handling
  errors: [{
    code: String,
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    }
  }],
  
  // Conflict Resolution
  conflicts: [{
    field: String,
    localValue: mongoose.Schema.Types.Mixed,
    serverValue: mongoose.Schema.Types.Mixed,
    resolution: {
      type: String,
      enum: ['local_wins', 'server_wins', 'merge', 'manual'],
      default: 'server_wins'
    },
    resolvedAt: Date,
    resolvedBy: String
  }],
  
  // Timing Information
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: {
    type: Date
  },
  duration: {
    type: Number // in milliseconds
  },
  
  // Network Information
  networkInfo: {
    connectionType: String, // wifi, cellular, etc.
    bandwidth: Number,
    latency: Number
  },
  
  // App Context
  appVersion: String,
  osVersion: String,
  
  // Retry Information
  retryCount: {
    type: Number,
    default: 0
  },
  maxRetries: {
    type: Number,
    default: 3
  },
  nextRetryAt: Date,
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes
syncDataSchema.index({ userId: 1, createdAt: -1 });
syncDataSchema.index({ deviceId: 1, createdAt: -1 });
syncDataSchema.index({ status: 1, createdAt: -1 });
syncDataSchema.index({ syncType: 1, dataType: 1 });
syncDataSchema.index({ startedAt: -1 });
syncDataSchema.index({ completedAt: -1 });

// Pre-save middleware
syncDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate duration if completed
  if (this.completedAt && this.startedAt) {
    this.duration = this.completedAt.getTime() - this.startedAt.getTime();
  }
  
  // Calculate progress percentage
  if (this.progress.total > 0) {
    this.progress.percentage = Math.round((this.progress.completed / this.progress.total) * 100);
  }
  
  next();
});

// Methods
syncDataSchema.methods.markCompleted = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  this.duration = this.completedAt.getTime() - this.startedAt.getTime();
  return this.save();
};

syncDataSchema.methods.markFailed = function(error) {
  this.status = 'failed';
  this.completedAt = new Date();
  this.duration = this.completedAt.getTime() - this.startedAt.getTime();
  
  if (error) {
    this.errors.push({
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'Unknown error occurred',
      severity: error.severity || 'medium'
    });
  }
  
  return this.save();
};

syncDataSchema.methods.updateProgress = function(completed, total) {
  this.progress.completed = completed;
  this.progress.total = total || this.progress.total;
  this.progress.percentage = Math.round((completed / this.progress.total) * 100);
  return this.save();
};

syncDataSchema.methods.addConflict = function(field, localValue, serverValue) {
  this.conflicts.push({
    field,
    localValue,
    serverValue
  });
  return this.save();
};

syncDataSchema.methods.canRetry = function() {
  return this.retryCount < this.maxRetries && this.status === 'failed';
};

syncDataSchema.methods.scheduleRetry = function(delayMinutes = 5) {
  this.retryCount += 1;
  this.nextRetryAt = new Date(Date.now() + (delayMinutes * 60 * 1000));
  this.status = 'pending';
  return this.save();
};

// Static methods
syncDataSchema.statics.findPendingSyncs = function() {
  return this.find({ 
    status: 'pending',
    $or: [
      { nextRetryAt: { $exists: false } },
      { nextRetryAt: { $lte: new Date() } }
    ]
  }).sort({ createdAt: 1 });
};

syncDataSchema.statics.findByUser = function(userId, limit = 50) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .limit(limit);
};

syncDataSchema.statics.findByDevice = function(deviceId, limit = 50) {
  return this.find({ deviceId })
    .sort({ createdAt: -1 })
    .limit(limit);
};

syncDataSchema.statics.getFailedSyncs = function() {
  return this.find({ status: 'failed' })
    .sort({ createdAt: -1 });
};

syncDataSchema.statics.getSyncStats = function(userId, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        userId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalSize: { $sum: '$dataSize' },
        avgDuration: { $avg: '$duration' }
      }
    }
  ]);
};

module.exports = mongoose.model('SyncData', syncDataSchema);
