2025-08-11 09:31:59:3159 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:31:59 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 93326,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94760960,
      "heapTotal": 39792640,
      "heapUsed": 27931880,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.49,
      3.08,
      2.39
    ],
    "uptime": 152686.25
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:32:11:3211 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:32:11 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 93454,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94965760,
      "heapTotal": 40316928,
      "heapUsed": 27401720,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.26,
      3.04,
      2.39
    ],
    "uptime": 152698.22
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:32:49:3249 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:32:49 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 93518,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94547968,
      "heapTotal": 40054784,
      "heapUsed": 27936232,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.41,
      3.1,
      2.44
    ],
    "uptime": 152736.53
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:33:08:338 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:33:08 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 93564,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 93962240,
      "heapTotal": 39006208,
      "heapUsed": 29369072,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.18,
      3.06,
      2.44
    ],
    "uptime": 152755.37
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:43:32:4332 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:43:32 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 98228,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95559680,
      "heapTotal": 58404864,
      "heapUsed": 25477248,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      7.57,
      7.27,
      5.11
    ],
    "uptime": 153379.31
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:44:28:4428 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:44:28 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 98605,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94507008,
      "heapTotal": 40054784,
      "heapUsed": 28127280,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      6.01,
      6.87,
      5.09
    ],
    "uptime": 153435.77
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:51:37:5137 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:51:37 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 100452,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 80367616,
      "heapTotal": 26161152,
      "heapUsed": 24170832,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      16.96,
      12.08,
      7.99
    ],
    "uptime": 153864.1
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 09:56:09:569 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 09:56:09 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 102648,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 84934656,
      "heapTotal": 35074048,
      "heapUsed": 24963408,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      8.8,
      10.75,
      8.61
    ],
    "uptime": 154135.4
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 10:09:44:944 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 10:09:44 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 106388,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 89436160,
      "heapTotal": 39792640,
      "heapUsed": 29466040,
      "external": 20396720,
      "arrayBuffers": 18262544
    }
  },
  "os": {
    "loadavg": [
      6.58,
      7.7,
      8.37
    ],
    "uptime": 154950.13
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 10:10:08:108 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 10:10:08 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 106727,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94060544,
      "heapTotal": 39792640,
      "heapUsed": 27970680,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      6.03,
      7.5,
      8.29
    ],
    "uptime": 154974.21
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 10:11:00:110 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/ClinicalRecord'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/ClinicalRecord'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:2:24)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 10:11:00 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 106822,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94392320,
      "heapTotal": 40054784,
      "heapUsed": 28558040,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      5.17,
      7.04,
      8.08
    ],
    "uptime": 155026.7
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 24,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 2,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 10:12:10:1210 [[31merror[39m]: [31muncaughtException: Cannot find module '../models/Diagnosis'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31mError: Cannot find module '../models/Diagnosis'[39m
[31mRequire stack:[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js[39m
[31m- /home/<USER>/Desktop/nso_v11/backend/server.js[39m
[31m    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1070:27)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:3:19)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
Error: Cannot find module '../models/Diagnosis'
Require stack:
- /home/<USER>/Desktop/nso_v11/backend/services/syncService.js
- /home/<USER>/Desktop/nso_v11/backend/routes/sync.js
- /home/<USER>/Desktop/nso_v11/backend/server.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1244:15)
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/services/syncService.js:3:19)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
{
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ]
  },
  "exception": true,
  "date": "Mon Aug 11 2025 10:12:10 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 107016,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94978048,
      "heapTotal": 40579072,
      "heapUsed": 28395224,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      6.63,
      7,
      7.99
    ],
    "uptime": 155096.25
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._resolveFilename",
      "line": 1244,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1070,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/Desktop/nso_v11/backend/services/syncService.js",
      "function": null,
      "line": 3,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    }
  ]
}
2025-08-11 10:13:28:1328 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:13:28 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 107298,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94601216,
      "heapTotal": 40054784,
      "heapUsed": 30374128,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      6.95,
      7.05,
      7.93
    ],
    "uptime": 155174.58
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:13:51:1351 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:13:51 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 107404,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95002624,
      "heapTotal": 40579072,
      "heapUsed": 29407160,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      7.31,
      7.14,
      7.94
    ],
    "uptime": 155197.86
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:34:40:3440 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:34:40 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 112050,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 84647936,
      "heapTotal": 29831168,
      "heapUsed": 25403304,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      13.4,
      11.42,
      10.04
    ],
    "uptime": 156446.52
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:35:40:3540 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:35:40 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 112471,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95797248,
      "heapTotal": 40316928,
      "heapUsed": 29493880,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      12.8,
      11.62,
      10.2
    ],
    "uptime": 156506.75
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:36:07:367 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:36:07 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 112603,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 96702464,
      "heapTotal": 58929152,
      "heapUsed": 26891408,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      12.52,
      11.63,
      10.24
    ],
    "uptime": 156533.58
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:36:47:3647 [[31merror[39m]: [31muncaughtException: validate is not a function[39m
[31mTypeError: validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)[39m
TypeError: validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:30)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:16:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:36:47 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 112964,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 96219136,
      "heapTotal": 40316928,
      "heapUsed": 29874856,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      11.52,
      11.5,
      10.25
    ],
    "uptime": 156573.41
  },
  "trace": [
    {
      "column": 30,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 16,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:40:03:403 [[31merror[39m]: [31muncaughtException: validate.validate is not a function[39m
[31mTypeError: validate.validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/auth.js:44:35)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:15:20)[39m
TypeError: validate.validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/auth.js:44:35)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:15:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:40:03 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 113409,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94646272,
      "heapTotal": 39215104,
      "heapUsed": 28902960,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      6.26,
      9.07,
      9.55
    ],
    "uptime": 156769.27
  },
  "trace": [
    {
      "column": 35,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/auth.js",
      "function": null,
      "line": 44,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 15,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:40:30:4030 [[31merror[39m]: [31muncaughtException: validate.validate is not a function[39m
[31mTypeError: validate.validate is not a function[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/auth.js:44:35)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
[31m    at require (node:internal/modules/helpers:136:16)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:15:20)[39m
TypeError: validate.validate is not a function
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/auth.js:44:35)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:15:20)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:40:30 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 113458,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95297536,
      "heapTotal": 40316928,
      "heapUsed": 26663952,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      5.89,
      8.71,
      9.41
    ],
    "uptime": 156796.95
  },
  "trace": [
    {
      "column": 35,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/auth.js",
      "function": null,
      "line": 44,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    },
    {
      "column": 16,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 136,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 15,
      "method": null,
      "native": false
    }
  ]
}
2025-08-11 10:44:08:448 [[31merror[39m]: [31muncaughtException: Route.post() requires a callback function but got a [object Object][39m
[31mError: Route.post() requires a callback function but got a [object Object][39m
[31m    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)[39m
[31m    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
Error: Route.post() requires a callback function but got a [object Object]
    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)
    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:44:08 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 114144,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 97280000,
      "heapTotal": 59723776,
      "heapUsed": 26418216,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      12.32,
      10.55,
      9.95
    ],
    "uptime": 157014.15
  },
  "trace": [
    {
      "column": 15,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js",
      "function": "Route.<computed> [as post]",
      "line": 216,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "proto.<computed> [as post]",
      "line": 521,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-11 10:44:41:4441 [[31merror[39m]: [31muncaughtException: Route.post() requires a callback function but got a [object Object][39m
[31m/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216[39m
[31m        throw new Error(msg);[39m
[31m        ^[39m

[31mError: Route.post() requires a callback function but got a [object Object][39m
[31m    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)[39m
[31m    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216
        throw new Error(msg);
        ^

Error: Route.post() requires a callback function but got a [object Object]
    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)
    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:44:41 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 114232,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node"
    ],
    "memoryUsage": {
      "rss": 95055872,
      "heapTotal": 40316928,
      "heapUsed": 29586368,
      "external": 20463070,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      12.2,
      10.72,
      10.03
    ],
    "uptime": 157047.56
  },
  "trace": [
    {
      "column": 15,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js",
      "function": "Route.<computed> [as post]",
      "line": 216,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "proto.<computed> [as post]",
      "line": 521,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    }
  ]
}
2025-08-11 10:49:05:495 [[31merror[39m]: [31muncaughtException: Route.post() requires a callback function but got a [object Object][39m
[31mError: Route.post() requires a callback function but got a [object Object][39m
[31m    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)[39m
[31m    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
[31m    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1335:12)[39m
Error: Route.post() requires a callback function but got a [object Object]
    at Route.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js:216:15)
    at proto.<computed> [as post] (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:521:19)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/routes/sync.js:23:8)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.require (node:internal/modules/cjs/loader:1335:12)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 10:49:04 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 114590,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 92946432,
      "heapTotal": 39530496,
      "heapUsed": 27128672,
      "external": 20396720,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      13.19,
      9.68,
      9.58
    ],
    "uptime": 157310.99
  },
  "trace": [
    {
      "column": 15,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/route.js",
      "function": "Route.<computed> [as post]",
      "line": 216,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "proto.<computed> [as post]",
      "line": 521,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "/home/<USER>/Desktop/nso_v11/backend/routes/sync.js",
      "function": null,
      "line": 23,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    },
    {
      "column": 24,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapModuleLoad",
      "line": 217,
      "method": null,
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1335,
      "method": "require",
      "native": false
    }
  ]
}
